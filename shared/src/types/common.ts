// Common types used across the application

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginationResponse {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface ApiResponse<T = any> {
  message: string;
  success: boolean;
  data?: T;
}

export interface ErrorResponse {
  message: string;
  success: false;
  error?: string;
}

export type Variables = {
  userId: string;
};
